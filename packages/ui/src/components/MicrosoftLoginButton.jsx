import React, { useState, useEffect } from 'react'
import { Button, CircularProgress } from '@mui/material'
import { InteractionRequiredAuthError } from '@azure/msal-browser'
import userApi from '@/api/user'
import { useDispatch } from 'react-redux'
import { logoutAction } from '@/store/actions'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'
import useMSAL from '@/hooks/useMSAL'

const MicrosoftLoginButton = ({ onError = () => {} }) => {
  const [isLoading, setIsLoading] = useState(false)
  const dispatch = useDispatch()
  const logout = (...args) => dispatch(logoutAction(...args))
  
  // Use MSAL hook
  const { isInitialized, error: msalError, login, handleRedirectPromise } = useMSAL()

  useEffect(() => {
    if (!isInitialized) return

    const handleMicrosoftCallback = async () => {
      // Check if we're on the callback URL with authorization code
      const urlParams = new URLSearchParams(window.location.search)
      const code = urlParams.get('code')
      const state = urlParams.get('state')
      
      microsoftOAuthDebugger.debugUrlParams()
      
      if (code) {
        microsoftOAuthDebugger.info('Authorization code found in URL, processing Microsoft callback', {
          hasCode: !!code,
          hasState: !!state,
          url: window.location.href
        })
        
        setIsLoading(true)
        
        try {
          const response = await handleRedirectPromise()
          
          if (response) {
            const accessToken = response.accessToken
            microsoftOAuthDebugger.info('Access token extracted from MSAL response', {
              tokenLength: accessToken?.length,
              tokenPreview: accessToken?.substring(0, 20) + '...'
            })

            microsoftOAuthDebugger.info('Sending access token to backend API')
            const loginResponse = await userApi.loginWithMicrosoft({ accessToken })
            
            microsoftOAuthDebugger.success('Backend API response received', {
              status: loginResponse.status,
              hasUser: !!loginResponse.data?.user,
              hasTokens: !!(loginResponse.data?.accessToken && loginResponse.data?.refreshToken)
            })

            if (loginResponse.data && loginResponse.data.user) {
              // Store login data
              localStorage.setItem('dataLogin', JSON.stringify(loginResponse.data))
              
              microsoftOAuthDebugger.success('Microsoft OAuth login successful', {
                userId: loginResponse.data.user.id,
                username: loginResponse.data.user.username
              })
              
              // Redirect to main application
              const redirectPath = localStorage.getItem('redirectAfterLogin') || '/'
              localStorage.removeItem('redirectAfterLogin')
              window.location.href = redirectPath
            } else {
              microsoftOAuthDebugger.error('Invalid response from backend API', loginResponse)
              onError('Phản hồi không hợp lệ từ server. Vui lòng thử lại.')
            }
          } else {
            microsoftOAuthDebugger.warning('No response from handleRedirectPromise')
            onError('Không nhận được phản hồi từ Microsoft. Vui lòng thử lại.')
          }
        } catch (error) {
          microsoftOAuthDebugger.error('Error during Microsoft login process', {
            error: error.message,
            response: error.response?.data,
            status: error.response?.status
          })
          
          if (error?.response?.data?.message) {
            onError(error?.response?.data?.message)
          } else {
            onError('Đã có lỗi xảy ra khi đăng nhập bằng Microsoft. Vui lòng thử lại sau.')
          }
          localStorage.removeItem('dataLogin')
          logout({})
        } finally {
          microsoftOAuthDebugger.info('Microsoft login process completed, setting loading to false')
          setIsLoading(false)
        }
      } else {
        microsoftOAuthDebugger.debug('No authorization code found in URL')
      }
    }

    handleMicrosoftCallback()
  }, [isInitialized, handleRedirectPromise, onError])

  const handleMicrosoftLogin = async () => {
    if (!isInitialized) {
      microsoftOAuthDebugger.error('MSAL not initialized, cannot proceed with login')
      onError('Microsoft Authentication chưa sẵn sàng. Vui lòng thử lại sau.')
      return
    }

    if (msalError) {
      microsoftOAuthDebugger.error('MSAL has error, cannot proceed with login', msalError)
      onError('Microsoft Authentication có lỗi. Vui lòng thử lại sau.')
      return
    }

    microsoftOAuthDebugger.info('Microsoft login button clicked')
    setIsLoading(true)
    
    try {
      await login(['User.Read'])
    } catch (error) {
      microsoftOAuthDebugger.error('Error during Microsoft login initiation', {
        error: error.message,
        errorType: error.constructor.name
      })
      
      if (error instanceof InteractionRequiredAuthError) {
        microsoftOAuthDebugger.warning('User cancelled the login (InteractionRequiredAuthError)')
        setIsLoading(false)
      } else {
        microsoftOAuthDebugger.error('Unexpected error during login initiation')
        onError('Đã có lỗi xảy ra khi khởi tạo đăng nhập Microsoft.')
        setIsLoading(false)
      }
    }
  }

  microsoftOAuthDebugger.debug('Rendering Microsoft login button', { 
    isLoading, 
    isInitialized, 
    hasError: !!msalError 
  })

  return (
    <Button
      fullWidth
      variant="outlined"
      size="large"
      disabled={isLoading || !isInitialized}
      onClick={handleMicrosoftLogin}
      sx={{
        py: 1.5,
        fontSize: { xs: '0.875rem', sm: '1rem' },
        borderColor: '#0078d4',
        color: '#0078d4',
        backgroundColor: 'white',
        '&:hover': {
          backgroundColor: '#f3f2f1',
          borderColor: '#106ebe'
        },
        '&:disabled': {
          backgroundColor: '#f3f2f1',
          borderColor: '#c8c6c4',
          color: '#a19f9d'
        }
      }}
      startIcon={
        isLoading ? (
          <CircularProgress size={20} sx={{ color: '#0078d4' }} />
        ) : (
          <svg width="20" height="20" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill="#f25022" d="M1 1h9v9H1z"/>
            <path fill="#00a4ef" d="M11 1h9v9h-9z"/>
            <path fill="#7fba00" d="M1 11h9v9H1z"/>
            <path fill="#ffb900" d="M11 11h9v9h-9z"/>
          </svg>
        )
      }
    >
      {isLoading ? 'Đang xử lý...' : 'Đăng nhập với Microsoft'}
    </Button>
  )
}

export default MicrosoftLoginButton
