import React, { useState, useEffect } from 'react'
import { Button, CircularProgress } from '@mui/material'
import { InteractionRequiredAuthError } from '@azure/msal-browser'
import userApi from '@/api/user'
import { useDispatch } from 'react-redux'
import { logoutAction } from '@/store/actions'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'
import useMSAL from '@/hooks/useMSAL'

const MicrosoftLoginButton = ({ onError = () => {} }) => {
  const [isLoading, setIsLoading] = useState(false)
  const dispatch = useDispatch()
  const logout = (...args) => dispatch(logoutAction(...args))
  
  // Use MSAL hook
  const { isInitialized, error: msalError, login, handleRedirectPromise } = useMSAL()

  useEffect(() => {
    if (!isInitialized) return

    // Handle Microsoft OAuth callback
    const handleMicrosoftCallback = async () => {
      microsoftOAuthDebugger.info('Checking for Microsoft OAuth callback')
      microsoftOAuthDebugger.debugURLParams()
      microsoftOAuthDebugger.debugLocalStorage()
      
      const urlParams = new URLSearchParams(window.location.search)
      const code = urlParams.get('code')
      const error = urlParams.get('error')
      const state = urlParams.get('state')

      microsoftOAuthDebugger.info('URL parameters found', { code: !!code, error, state: !!state })

      if (error) {
        microsoftOAuthDebugger.error('Microsoft OAuth error received', { error })
        onError('Đăng nhập Microsoft thất bại: ' + error)
        return
      }

      if (code) {
        microsoftOAuthDebugger.info('Authorization code received, starting token exchange')
        setIsLoading(true)
        
        try {
          const response = await handleRedirectPromise()
          
          if (response) {
            const accessToken = response.accessToken
            microsoftOAuthDebugger.info('Access token extracted from MSAL response', {
              tokenLength: accessToken?.length,
              tokenPreview: accessToken?.substring(0, 20) + '...'
            })

            microsoftOAuthDebugger.info('Sending access token to backend API')
            const loginResponse = await userApi.loginWithMicrosoft({ accessToken })
            
            microsoftOAuthDebugger.success('Backend API response received', {
              status: loginResponse.status,
              hasUser: !!loginResponse.data?.user,
              hasTokens: !!(loginResponse.data?.accessToken && loginResponse.data?.refreshToken)
            })
            
            const resData = loginResponse.data
            
            if (resData) {
              microsoftOAuthDebugger.success('Login successful, storing data in localStorage')
              localStorage.setItem('dataLogin', JSON.stringify(resData))
              
              const redirectUrl = localStorage.getItem('redirectAfterLogin')
              if (redirectUrl) {
                microsoftOAuthDebugger.info('Redirecting to saved URL', { redirectUrl })
                localStorage.removeItem('redirectAfterLogin')
                window.location.href = redirectUrl
              } else {
                microsoftOAuthDebugger.info('Redirecting to home page')
                window.location.href = ''
              }
            } else {
              microsoftOAuthDebugger.error('No data received from backend API')
              throw new Error('No data received from backend')
            }
          } else {
            microsoftOAuthDebugger.warning('No MSAL redirect response received')
          }
        } catch (error) {
          microsoftOAuthDebugger.error('Error during Microsoft login process', {
            error: error.message,
            response: error.response?.data,
            status: error.response?.status
          })
          
          if (error?.response?.data?.message) {
            onError(error?.response?.data?.message)
          } else {
            onError('Đã có lỗi xảy ra khi đăng nhập bằng Microsoft. Vui lòng thử lại sau.')
          }
          localStorage.removeItem('dataLogin')
          logout({})
        } finally {
          microsoftOAuthDebugger.info('Microsoft login process completed, setting loading to false')
          setIsLoading(false)
        }
      } else {
        microsoftOAuthDebugger.debug('No authorization code found in URL')
      }
    }

    handleMicrosoftCallback()
  }, [isInitialized, handleRedirectPromise, onError])

  const handleMicrosoftLogin = async () => {
    if (!isInitialized) {
      microsoftOAuthDebugger.error('MSAL not initialized, cannot proceed with login')
      onError('Microsoft Authentication chưa sẵn sàng. Vui lòng thử lại sau.')
      return
    }

    if (msalError) {
      microsoftOAuthDebugger.error('MSAL has error, cannot proceed with login', msalError)
      onError('Microsoft Authentication có lỗi. Vui lòng thử lại sau.')
      return
    }

    microsoftOAuthDebugger.info('Microsoft login button clicked')
    setIsLoading(true)
    
    try {
      await login(['User.Read'])
    } catch (error) {
      microsoftOAuthDebugger.error('Error during Microsoft login initiation', {
        error: error.message,
        errorType: error.constructor.name
      })
      
      if (error instanceof InteractionRequiredAuthError) {
        microsoftOAuthDebugger.warning('User cancelled the login (InteractionRequiredAuthError)')
        setIsLoading(false)
      } else {
        microsoftOAuthDebugger.error('Unexpected error during login initiation')
        onError('Đã có lỗi xảy ra khi khởi tạo đăng nhập Microsoft.')
        setIsLoading(false)
      }
    }
  }

  microsoftOAuthDebugger.debug('Rendering Microsoft login button', { 
    isLoading, 
    isInitialized, 
    hasError: !!msalError 
  })

  return (
    <Button
      fullWidth
      variant='outlined'
      size='large'
      disabled={isLoading || !isInitialized || !!msalError}
      onClick={handleMicrosoftLogin}
      sx={{
        py: 1.5,
        fontSize: { xs: '0.875rem', sm: '1rem' },
        borderColor: '#0078d4',
        color: '#0078d4',
        '&:hover': {
          borderColor: '#106ebe',
          backgroundColor: 'rgba(0, 120, 212, 0.04)'
        },
        '&:disabled': {
          borderColor: '#ccc',
          color: '#ccc'
        }
      }}
      startIcon={
        isLoading ? (
          <CircularProgress size={20} color='primary' />
        ) : !isInitialized ? (
          <CircularProgress size={20} color='primary' />
        ) : (
          <svg width="20" height="20" viewBox="0 0 21 21" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 1H1V10H10V1Z" fill="#F25022"/>
            <path d="M20 1H11V10H20V1Z" fill="#7FBA00"/>
            <path d="M10 11H1V20H10V11Z" fill="#00A4EF"/>
            <path d="M20 11H11V20H20V11Z" fill="#FFB900"/>
          </svg>
        )
      }
    >
      {isLoading ? 'Đang xử lý...' : !isInitialized ? 'Đang khởi tạo...' : msalError ? 'Lỗi khởi tạo' : 'Đăng nhập bằng Microsoft'}
    </Button>
  )
}

export default MicrosoftLoginButton
