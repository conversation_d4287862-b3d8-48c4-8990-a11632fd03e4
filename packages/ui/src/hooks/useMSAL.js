import { useState, useEffect, useRef } from 'react'
import { PublicClientApplication, InteractionStatus } from '@azure/msal-browser'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const useMSAL = () => {
  const [msalInstance, setMsalInstance] = useState(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState(null)
  const [isInteractionInProgress, setIsInteractionInProgress] = useState(false)
  const interactionInProgressRef = useRef(false)

  useEffect(() => {
    const initializeMSAL = async () => {
      try {
        // MSAL Configuration
        const msalConfig = {
          auth: {
            clientId: process.env.REACT_APP_MICROSOFT_CLIENT_ID || '627f8a73-606b-46b2-b72a-621402ddc719',
            authority: process.env.REACT_APP_MICROSOFT_TENANT_ID || '2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0'
              ? `https://login.microsoftonline.com/2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0`
              : 'https://login.microsoftonline.com/common',
            redirectUri: window.location.origin + '/redirect'
          },
          cache: {
            cacheLocation: 'sessionStorage',
            storeAuthStateInCookie: false
          }
        }

        microsoftOAuthDebugger.debugMSALConfig(msalConfig)
        microsoftOAuthDebugger.info('Creating MSAL instance')
        
        const instance = new PublicClientApplication(msalConfig)
        setMsalInstance(instance)
        
        microsoftOAuthDebugger.info('Initializing MSAL instance')
        await instance.initialize()
        
        microsoftOAuthDebugger.success('MSAL instance initialized successfully')
        setIsInitialized(true)
        setError(null)

        // Always call handleRedirectPromise on mount
        setIsInteractionInProgress(true)
        interactionInProgressRef.current = true
        await instance.handleRedirectPromise()
        setIsInteractionInProgress(false)
        interactionInProgressRef.current = false
        microsoftOAuthDebugger.info('handleRedirectPromise completed on mount')
      } catch (err) {
        microsoftOAuthDebugger.error('Failed to initialize MSAL instance', {
          error: err.message,
          errorType: err.constructor.name,
          stack: err.stack
        })
        setError(err)
        setIsInitialized(false)
        setIsInteractionInProgress(false)
        interactionInProgressRef.current = false
        console.error('MSAL initialization failed:', err)
      }
    }

    initializeMSAL()
  }, [])

  const login = async (scopes = ['User.Read']) => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    if (isInteractionInProgress || interactionInProgressRef.current) {
      microsoftOAuthDebugger.warning('Interaction already in progress, loginRedirect blocked')
      throw new Error('Đang có phiên đăng nhập Microsoft khác đang diễn ra. Vui lòng chờ...')
    }
    try {
      const loginRequest = { scopes }
      microsoftOAuthDebugger.info('Initiating Microsoft login redirect', loginRequest)
      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true
      await msalInstance.loginRedirect(loginRequest)
      microsoftOAuthDebugger.success('MSAL login redirect initiated successfully')
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      microsoftOAuthDebugger.error('Error during Microsoft login initiation', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const handleRedirectPromise = async () => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    try {
      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true
      microsoftOAuthDebugger.info('Handling redirect promise with MSAL')
      const response = await msalInstance.handleRedirectPromise()
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      if (response) {
        microsoftOAuthDebugger.success('MSAL redirect response received', {
          account: response.account?.username,
          scopes: response.scopes,
          expiresOn: response.expiresOn
        })
      } else {
        microsoftOAuthDebugger.warning('No MSAL redirect response received')
      }
      return response
    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      microsoftOAuthDebugger.error('Error handling redirect promise', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  return {
    msalInstance,
    isInitialized,
    error,
    login,
    handleRedirectPromise,
    isInteractionInProgress
  }
}

export default useMSAL
