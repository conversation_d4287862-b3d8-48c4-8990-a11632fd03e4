import React, { useEffect, useState } from 'react'
import { Box, CircularProgress, Typography, Alert } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { loginAction } from '@/store/actions'
import useMSAL from '@/hooks/useMSAL'
import userApi from '@/api/user'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const MicrosoftRedirect = () => {
  const [status, setStatus] = useState('processing') // processing, success, error
  const [errorMessage, setErrorMessage] = useState('')
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const login = (...args) => dispatch(loginAction(...args))
  
  const { isInitialized, handleRedirectPromise } = useMSAL()

  useEffect(() => {
    const handleMicrosoftRedirect = async () => {
      if (!isInitialized) {
        microsoftOAuthDebugger.info('MSAL not initialized yet, waiting...')
        return
      }

      try {
        microsoftOAuthDebugger.info('Microsoft redirect page loaded, handling redirect promise')
        setStatus('processing')

        // Handle the redirect response from Microsoft
        const response = await handleRedirectPromise()
        
        if (!response) {
          microsoftOAuthDebugger.warning('No redirect response received from MSAL')
          setStatus('error')
          setErrorMessage('Không nhận được phản hồi từ Microsoft. Vui lòng thử lại.')
          return
        }

        const accessToken = response.accessToken
        if (!accessToken) {
          microsoftOAuthDebugger.error('No access token in MSAL response')
          setStatus('error')
          setErrorMessage('Không nhận được access token từ Microsoft. Vui lòng thử lại.')
          return
        }

        microsoftOAuthDebugger.success('Access token received from MSAL', {
          tokenLength: accessToken.length,
          tokenPreview: accessToken.substring(0, 20) + '...',
          account: response.account?.username
        })

        // Send access token to backend for validation and user creation/login
        microsoftOAuthDebugger.info('Sending access token to backend API')
        const loginResponse = await userApi.loginWithMicrosoft({ accessToken })
        
        if (loginResponse.data && loginResponse.data.user) {
          microsoftOAuthDebugger.success('Backend login successful', {
            userId: loginResponse.data.user.id,
            username: loginResponse.data.user.username,
            hasTokens: !!(loginResponse.data.accessToken && loginResponse.data.refreshToken)
          })

          // Store login data
          localStorage.setItem('dataLogin', JSON.stringify(loginResponse.data))
          
          // Update Redux store
          login(loginResponse.data.user)
          
          setStatus('success')
          
          // Redirect to the original page or home
          const redirectPath = localStorage.getItem('redirectAfterLogin') || '/'
          localStorage.removeItem('redirectAfterLogin')
          
          microsoftOAuthDebugger.success('Redirecting to application', { redirectPath })
          
          setTimeout(() => {
            navigate(redirectPath)
          }, 1000)
        } else {
          microsoftOAuthDebugger.error('Invalid response from backend API', loginResponse)
          setStatus('error')
          setErrorMessage('Phản hồi không hợp lệ từ server. Vui lòng thử lại.')
        }
      } catch (error) {
        microsoftOAuthDebugger.error('Error during Microsoft redirect handling', {
          error: error.message,
          errorType: error.constructor.name,
          response: error.response?.data
        })
        
        setStatus('error')
        
        if (error.response?.data?.message) {
          setErrorMessage(error.response.data.message)
        } else if (error.message) {
          setErrorMessage(`Lỗi: ${error.message}`)
        } else {
          setErrorMessage('Đã có lỗi xảy ra trong quá trình đăng nhập Microsoft. Vui lòng thử lại.')
        }
      }
    }

    handleMicrosoftRedirect()
  }, [isInitialized, handleRedirectPromise, navigate, login])

  const handleRetry = () => {
    microsoftOAuthDebugger.info('User clicked retry, redirecting to login page')
    navigate('/login')
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      padding={3}
    >
      {status === 'processing' && (
        <>
          <CircularProgress size={60} sx={{ mb: 3 }} />
          <Typography variant="h6" gutterBottom>
            Đang xử lý đăng nhập Microsoft...
          </Typography>
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Vui lòng đợi trong khi chúng tôi xác thực thông tin của bạn
          </Typography>
        </>
      )}

      {status === 'success' && (
        <>
          <Box
            sx={{
              width: 60,
              height: 60,
              borderRadius: '50%',
              backgroundColor: 'success.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mb: 3
            }}
          >
            <Typography variant="h4" color="white">
              ✓
            </Typography>
          </Box>
          <Typography variant="h6" gutterBottom color="success.main">
            Đăng nhập thành công!
          </Typography>
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Đang chuyển hướng đến ứng dụng...
          </Typography>
        </>
      )}

      {status === 'error' && (
        <>
          <Alert severity="error" sx={{ mb: 3, maxWidth: 500 }}>
            <Typography variant="body1" gutterBottom>
              Đăng nhập Microsoft thất bại
            </Typography>
            <Typography variant="body2">
              {errorMessage}
            </Typography>
          </Alert>
          <Typography 
            variant="body2" 
            color="primary" 
            sx={{ cursor: 'pointer', textDecoration: 'underline' }}
            onClick={handleRetry}
          >
            Thử lại
          </Typography>
        </>
      )}
    </Box>
  )
}

export default MicrosoftRedirect
