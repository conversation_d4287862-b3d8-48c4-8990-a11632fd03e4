// Microsoft OAuth Authorization Code Storage Utility

const STORAGE_KEYS = {
  AUTH_CODE: 'microsoft_auth_code',
  AUTH_ERROR: 'microsoft_auth_error',
  AUTH_STATE: 'microsoft_auth_state',
  ACCESS_TOKEN: 'microsoft_access_token',
  REFRESH_TOKEN: 'microsoft_refresh_token'
}

class MicrosoftAuthStorage {
  
  // Lưu authorization code
  saveAuthCode(code, state = null, additionalData = {}) {
    const authData = {
      code: code,
      state: state,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      ...additionalData
    }
    
    localStorage.setItem(STORAGE_KEYS.AUTH_CODE, JSON.stringify(authData))
    console.log('🔐 [Auth Storage]: Authorization code saved', {
      codeLength: code.length,
      codePreview: code.substring(0, 20) + '...',
      state: state
    })
    
    return authData
  }
  
  // Lấy authorization code
  getAuthCode() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.AUTH_CODE)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('🔐 [Auth Storage]: Error parsing auth code', error)
      return null
    }
  }
  
  // Xóa authorization code
  clearAuthCode() {
    localStorage.removeItem(STORAGE_KEYS.AUTH_CODE)
    console.log('🔐 [Auth Storage]: Authorization code cleared')
  }
  
  // Lưu lỗi OAuth
  saveAuthError(error, errorDescription = null, additionalData = {}) {
    const errorData = {
      error: error,
      errorDescription: errorDescription,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      ...additionalData
    }
    
    localStorage.setItem(STORAGE_KEYS.AUTH_ERROR, JSON.stringify(errorData))
    console.log('🔐 [Auth Storage]: OAuth error saved', errorData)
    
    return errorData
  }
  
  // Lấy lỗi OAuth
  getAuthError() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.AUTH_ERROR)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('🔐 [Auth Storage]: Error parsing auth error', error)
      return null
    }
  }
  
  // Xóa lỗi OAuth
  clearAuthError() {
    localStorage.removeItem(STORAGE_KEYS.AUTH_ERROR)
    console.log('🔐 [Auth Storage]: OAuth error cleared')
  }
  
  // Lưu access token
  saveAccessToken(accessToken, expiresIn = null, additionalData = {}) {
    const tokenData = {
      accessToken: accessToken,
      expiresIn: expiresIn,
      timestamp: new Date().toISOString(),
      expiresAt: expiresIn ? new Date(Date.now() + expiresIn * 1000).toISOString() : null,
      ...additionalData
    }
    
    localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, JSON.stringify(tokenData))
    console.log('🔐 [Auth Storage]: Access token saved', {
      tokenLength: accessToken.length,
      tokenPreview: accessToken.substring(0, 20) + '...',
      expiresIn: expiresIn
    })
    
    return tokenData
  }
  
  // Lấy access token
  getAccessToken() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)
      if (!data) return null
      
      const tokenData = JSON.parse(data)
      
      // Kiểm tra xem token có hết hạn không
      if (tokenData.expiresAt && new Date() > new Date(tokenData.expiresAt)) {
        console.log('🔐 [Auth Storage]: Access token expired, clearing')
        this.clearAccessToken()
        return null
      }
      
      return tokenData
    } catch (error) {
      console.error('🔐 [Auth Storage]: Error parsing access token', error)
      return null
    }
  }
  
  // Xóa access token
  clearAccessToken() {
    localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN)
    console.log('🔐 [Auth Storage]: Access token cleared')
  }
  
  // Lưu refresh token
  saveRefreshToken(refreshToken, additionalData = {}) {
    const tokenData = {
      refreshToken: refreshToken,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
    
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, JSON.stringify(tokenData))
    console.log('🔐 [Auth Storage]: Refresh token saved')
    
    return tokenData
  }
  
  // Lấy refresh token
  getRefreshToken() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('🔐 [Auth Storage]: Error parsing refresh token', error)
      return null
    }
  }
  
  // Xóa refresh token
  clearRefreshToken() {
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
    console.log('🔐 [Auth Storage]: Refresh token cleared')
  }
  
  // Lưu state
  saveState(state, additionalData = {}) {
    const stateData = {
      state: state,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
    
    localStorage.setItem(STORAGE_KEYS.AUTH_STATE, JSON.stringify(stateData))
    console.log('🔐 [Auth Storage]: Auth state saved', { state })
    
    return stateData
  }
  
  // Lấy state
  getState() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.AUTH_STATE)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('🔐 [Auth Storage]: Error parsing auth state', error)
      return null
    }
  }
  
  // Xóa state
  clearState() {
    localStorage.removeItem(STORAGE_KEYS.AUTH_STATE)
    console.log('🔐 [Auth Storage]: Auth state cleared')
  }
  
  // Xóa tất cả dữ liệu Microsoft OAuth
  clearAll() {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key)
    })
    console.log('🔐 [Auth Storage]: All Microsoft OAuth data cleared')
  }
  
  // Lấy tất cả dữ liệu Microsoft OAuth
  getAllData() {
    const data = {}
    Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
      try {
        const value = localStorage.getItem(key)
        data[name] = value ? JSON.parse(value) : null
      } catch (error) {
        data[name] = null
      }
    })
    return data
  }
  
  // Kiểm tra xem có authorization code không
  hasAuthCode() {
    return !!this.getAuthCode()
  }
  
  // Kiểm tra xem có access token hợp lệ không
  hasValidAccessToken() {
    const tokenData = this.getAccessToken()
    return !!tokenData && !!tokenData.accessToken
  }
  
  // Debug: hiển thị tất cả dữ liệu
  debugAll() {
    const data = this.getAllData()
    console.log('🔐 [Auth Storage]: All stored data', data)
    return data
  }
}

// Tạo instance global
const microsoftAuthStorage = new MicrosoftAuthStorage()

// Expose to window for debugging
if (typeof window !== 'undefined') {
  window.microsoftAuthStorage = microsoftAuthStorage
}

export default microsoftAuthStorage
