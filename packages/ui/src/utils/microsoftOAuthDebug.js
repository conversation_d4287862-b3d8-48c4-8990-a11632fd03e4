// Microsoft OAuth2 Debug Utility
class MicrosoftOAuthDebugger {
  constructor() {
    this.logs = []
    this.isEnabled = process.env.NODE_ENV === 'development' || localStorage.getItem('microsoft-oauth-debug') === 'true'
  }

  log(message, data = null, level = 'info') {
    if (!this.isEnabled) return

    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      level,
      message,
      data,
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    this.logs.push(logEntry)

    // Console output with emoji
    const emoji = {
      info: '🔍',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      debug: '🐛'
    }

    const consoleMethod = level === 'error' ? 'error' : level === 'warning' ? 'warn' : 'log'
    const logMessage = `${emoji[level]} [Microsoft OAuth Debug] [${timestamp}]: ${message}`
    
    if (data) {
      console[consoleMethod](logMessage, data)
    } else {
      console[consoleMethod](logMessage)
    }

    // Keep only last 100 logs
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-100)
    }
  }

  info(message, data = null) {
    this.log(message, data, 'info')
  }

  success(message, data = null) {
    this.log(message, data, 'success')
  }

  warning(message, data = null) {
    this.log(message, data, 'warning')
  }

  error(message, data = null) {
    this.log(message, data, 'error')
  }

  debug(message, data = null) {
    this.log(message, data, 'debug')
  }

  // Debug MSAL configuration
  debugMSALConfig(config) {
    this.info('MSAL Configuration', {
      clientId: config.auth.clientId,
      authority: config.auth.authority,
      redirectUri: config.auth.redirectUri,
      cacheLocation: config.cache.cacheLocation,
      storeAuthStateInCookie: config.cache.storeAuthStateInCookie
    })
  }

  // Debug URL parameters
  debugURLParams() {
    const urlParams = new URLSearchParams(window.location.search)
    const params = {}
    
    for (const [key, value] of urlParams.entries()) {
      params[key] = value
    }

    this.info('URL Parameters', params)
  }

  // Debug localStorage state
  debugLocalStorage() {
    const dataLogin = localStorage.getItem('dataLogin')
    const redirectAfterLogin = localStorage.getItem('redirectAfterLogin')
    
    this.info('LocalStorage State', {
      hasDataLogin: !!dataLogin,
      dataLoginPreview: dataLogin ? JSON.parse(dataLogin) : null,
      redirectAfterLogin
    })
  }

  // Debug network requests
  debugNetworkRequest(config) {
    this.info('Network Request', {
      method: config.method?.toUpperCase(),
      url: config.url,
      hasData: !!config.data,
      headers: {
        'content-type': config.headers?.['content-type'],
        'authorization': config.headers?.authorization ? 'Bearer ***' : undefined
      }
    })
  }

  // Debug network response
  debugNetworkResponse(response) {
    this.success('Network Response', {
      status: response.status,
      url: response.config.url,
      hasData: !!response.data,
      dataKeys: response.data ? Object.keys(response.data) : []
    })
  }

  // Debug network error
  debugNetworkError(error) {
    this.error('Network Error', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.message,
      responseData: error.response?.data
    })
  }

  // Get all logs
  getLogs() {
    return this.logs
  }

  // Export logs
  exportLogs() {
    const logData = {
      timestamp: new Date().toISOString(),
      logs: this.logs,
      environment: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        localStorage: {
          hasDataLogin: !!localStorage.getItem('dataLogin'),
          redirectAfterLogin: localStorage.getItem('redirectAfterLogin')
        }
      }
    }

    const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `microsoft-oauth-debug-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Clear logs
  clearLogs() {
    this.logs = []
    this.info('Logs cleared')
  }

  // Enable/disable debug mode
  setEnabled(enabled) {
    this.isEnabled = enabled
    localStorage.setItem('microsoft-oauth-debug', enabled.toString())
    this.info(`Debug mode ${enabled ? 'enabled' : 'disabled'}`)
  }
}

// Create global instance
const microsoftOAuthDebugger = new MicrosoftOAuthDebugger()

// Expose to window for debugging
if (typeof window !== 'undefined') {
  window.microsoftOAuthDebugger = microsoftOAuthDebugger
}

export default microsoftOAuthDebugger 