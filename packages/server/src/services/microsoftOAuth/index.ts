import { ConfidentialClientApplication, AuthenticationResult } from '@azure/msal-node'
import axios from 'axios'
import { StatusCodes } from 'http-status-codes'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getRunningExpressApp } from '../../utils'
import { User, UserRole } from '../../database/entities/User'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { v4 as uuidv4 } from 'uuid'

interface MicrosoftUserInfo {
  id: string
  displayName: string
  mail: string
  userPrincipalName: string
  givenName?: string
  surname?: string
}

interface MicrosoftOAuthConfig {
  clientId: string
  clientSecret: string
  tenantId: string
}

class MicrosoftOAuthService {
  private msalInstance: ConfidentialClientApplication | null = null
  private config: MicrosoftOAuthConfig

  constructor() {
    this.config = {
      clientId: process.env.MICROSOFT_CLIENT_ID || '',
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET || '',
      tenantId: process.env.MICROSOFT_TENANT_ID || 'common'
    }

    if (!this.config.clientId || !this.config.clientSecret) {
      console.warn('🔐 [Microsoft OAuth]: Missing Microsoft OAuth configuration')
      return
    }

    this.initializeMSAL()
  }

  private initializeMSAL() {
    try {
      const msalConfig = {
        auth: {
          clientId: this.config.clientId,
          clientSecret: this.config.clientSecret,
          authority: `https://login.microsoftonline.com/${this.config.tenantId}`
        }
      }

      this.msalInstance = new ConfidentialClientApplication(msalConfig)
      console.log('🔐 [Microsoft OAuth]: MSAL instance initialized successfully')
    } catch (error) {
      console.error('🔐 [Microsoft OAuth]: Failed to initialize MSAL instance', error)
      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to initialize Microsoft OAuth')
    }
  }

  async validateAccessToken(accessToken: string): Promise<MicrosoftUserInfo> {
    try {
      console.log('🔐 [Microsoft OAuth]: Validating access token with Microsoft Graph API')
      
      const response = await axios.get('https://graph.microsoft.com/v1.0/me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      })

      const userInfo: MicrosoftUserInfo = response.data
      console.log('🔐 [Microsoft OAuth]: Successfully validated access token', {
        userId: userInfo.id,
        displayName: userInfo.displayName,
        email: userInfo.mail || userInfo.userPrincipalName
      })

      return userInfo
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Failed to validate access token', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      })

      if (error.response?.status === 401) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Invalid or expired Microsoft access token')
      }

      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to validate Microsoft access token')
    }
  }

  async findOrCreateUser(microsoftUserInfo: MicrosoftUserInfo): Promise<User> {
    try {
      const appServer = getRunningExpressApp()
      const userRepository = appServer.AppDataSource.getRepository(User)

      // First, try to find user by Microsoft ID
      let user = await userRepository.findOne({
        where: { microsoftId: microsoftUserInfo.id }
      })

      if (user) {
        console.log('🔐 [Microsoft OAuth]: Found existing user by Microsoft ID', {
          userId: user.id,
          username: user.username
        })

        // Update user info if needed
        if (user.displayName !== microsoftUserInfo.displayName) {
          user.displayName = microsoftUserInfo.displayName
          await userRepository.save(user)
          console.log('🔐 [Microsoft OAuth]: Updated user display name')
        }

        return user
      }

      // Try to find user by email
      const email = microsoftUserInfo.mail || microsoftUserInfo.userPrincipalName
      user = await userRepository.findOne({
        where: { email: email }
      })

      if (user) {
        console.log('🔐 [Microsoft OAuth]: Found existing user by email, linking Microsoft account', {
          userId: user.id,
          email: email
        })

        // Link Microsoft account to existing user
        user.microsoftId = microsoftUserInfo.id
        user.displayName = microsoftUserInfo.displayName
        await userRepository.save(user)

        return user
      }

      // Create new user
      console.log('🔐 [Microsoft OAuth]: Creating new user from Microsoft account', {
        microsoftId: microsoftUserInfo.id,
        email: email,
        displayName: microsoftUserInfo.displayName
      })

      const newUser = new User()
      newUser.id = uuidv4()
      newUser.username = email
      newUser.email = email
      newUser.microsoftId = microsoftUserInfo.id
      newUser.displayName = microsoftUserInfo.displayName
      newUser.password = await bcrypt.hash(uuidv4(), 10) // Random password for OAuth users
      newUser.role = UserRole.USER
      newUser.active = true
      newUser.groupname = 'Microsoft_Users' // Default group for Microsoft OAuth users

      const savedUser = await userRepository.save(newUser)
      console.log('🔐 [Microsoft OAuth]: Successfully created new user', {
        userId: savedUser.id,
        username: savedUser.username
      })

      return savedUser
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Failed to find or create user', {
        error: error.message,
        microsoftId: microsoftUserInfo.id
      })
      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to process Microsoft user')
    }
  }

  async loginWithMicrosoft(accessToken: string): Promise<{ user: User; accessToken: string; refreshToken: string }> {
    try {
      console.log('🔐 [Microsoft OAuth]: Starting Microsoft OAuth login process')

      // Validate the access token with Microsoft Graph API
      const microsoftUserInfo = await this.validateAccessToken(accessToken)

      // Find or create user in our database
      const user = await this.findOrCreateUser(microsoftUserInfo)

      // Check if user is active
      if (!user.active) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Tài khoản đã bị vô hiệu hoá')
      }

      // Generate JWT tokens
      if (!process.env.ACCESS_TOKEN_SECRET || !process.env.REFRESH_TOKEN_SECRET) {
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Bí mật token không được xác định')
      }

      const jwtAccessToken = jwt.sign({ id: user.id, username: user.username }, process.env.ACCESS_TOKEN_SECRET)
      const jwtRefreshToken = jwt.sign({ id: user.id, username: user.username }, process.env.REFRESH_TOKEN_SECRET)

      console.log('🔐 [Microsoft OAuth]: Successfully completed Microsoft OAuth login', {
        userId: user.id,
        username: user.username
      })

      return {
        user,
        accessToken: jwtAccessToken,
        refreshToken: jwtRefreshToken
      }
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Microsoft OAuth login failed', {
        error: error.message
      })

      if (error instanceof InternalFlowiseError) {
        throw error
      }

      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Microsoft OAuth login failed')
    }
  }
}

export default new MicrosoftOAuthService()
