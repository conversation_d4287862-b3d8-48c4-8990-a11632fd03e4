/**
 * Microsoft OAuth Implementation Test Script
 * 
 * This script tests the Microsoft OAuth implementation by checking:
 * 1. Backend service initialization
 * 2. Environment variables configuration
 * 3. API endpoint accessibility
 * 4. Frontend component availability
 * 
 * Run this script to verify the implementation is working correctly.
 */

const fs = require('fs')
const path = require('path')

console.log('🔐 Microsoft OAuth Implementation Test\n')

// Test 1: Check Backend Files
console.log('1. Checking Backend Implementation...')

const backendFiles = [
  'packages/server/src/services/microsoftOAuth/index.ts',
  'packages/server/src/controllers/microsoftOAuth/index.ts',
  'packages/server/src/database/migrations/AddMicrosoftOAuthFields.ts'
]

backendFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`)
  } else {
    console.log(`   ❌ ${file} - Missing`)
  }
})

// Test 2: Check Frontend Files
console.log('\n2. Checking Frontend Implementation...')

const frontendFiles = [
  'packages/ui/src/hooks/useMSAL.js',
  'packages/ui/src/components/MicrosoftLoginButton.jsx',
  'packages/ui/src/views/MicrosoftRedirect/index.jsx',
  'packages/ui/src/utils/microsoftOAuthDebug.js'
]

frontendFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`)
  } else {
    console.log(`   ❌ ${file} - Missing`)
  }
})

// Test 3: Check Package Dependencies
console.log('\n3. Checking Package Dependencies...')

// Check backend package.json
const backendPackageJson = JSON.parse(fs.readFileSync('packages/server/package.json', 'utf8'))
const backendDeps = backendPackageJson.dependencies || {}

if (backendDeps['@azure/msal-node']) {
  console.log(`   ✅ Backend: @azure/msal-node@${backendDeps['@azure/msal-node']}`)
} else {
  console.log('   ❌ Backend: @azure/msal-node - Missing')
}

// Check frontend package.json
const frontendPackageJson = JSON.parse(fs.readFileSync('packages/ui/package.json', 'utf8'))
const frontendDeps = frontendPackageJson.dependencies || {}

if (frontendDeps['@azure/msal-browser']) {
  console.log(`   ✅ Frontend: @azure/msal-browser@${frontendDeps['@azure/msal-browser']}`)
} else {
  console.log('   ❌ Frontend: @azure/msal-browser - Missing')
}

if (frontendDeps['@azure/msal-react']) {
  console.log(`   ✅ Frontend: @azure/msal-react@${frontendDeps['@azure/msal-react']}`)
} else {
  console.log('   ❌ Frontend: @azure/msal-react - Missing')
}

// Test 4: Check Environment Configuration
console.log('\n4. Checking Environment Configuration...')

// Check backend .env
if (fs.existsSync('packages/server/.env')) {
  const backendEnv = fs.readFileSync('packages/server/.env', 'utf8')
  
  if (backendEnv.includes('MICROSOFT_CLIENT_ID')) {
    console.log('   ✅ Backend: MICROSOFT_CLIENT_ID configured')
  } else {
    console.log('   ⚠️  Backend: MICROSOFT_CLIENT_ID not configured')
  }
  
  if (backendEnv.includes('MICROSOFT_CLIENT_SECRET')) {
    console.log('   ✅ Backend: MICROSOFT_CLIENT_SECRET configured')
  } else {
    console.log('   ⚠️  Backend: MICROSOFT_CLIENT_SECRET not configured')
  }
  
  if (backendEnv.includes('MICROSOFT_TENANT_ID')) {
    console.log('   ✅ Backend: MICROSOFT_TENANT_ID configured')
  } else {
    console.log('   ⚠️  Backend: MICROSOFT_TENANT_ID not configured')
  }
} else {
  console.log('   ❌ Backend: .env file not found')
}

// Check frontend .env
if (fs.existsSync('packages/ui/.env')) {
  const frontendEnv = fs.readFileSync('packages/ui/.env', 'utf8')
  
  if (frontendEnv.includes('REACT_APP_MICROSOFT_CLIENT_ID')) {
    console.log('   ✅ Frontend: REACT_APP_MICROSOFT_CLIENT_ID configured')
  } else {
    console.log('   ⚠️  Frontend: REACT_APP_MICROSOFT_CLIENT_ID not configured')
  }
  
  if (frontendEnv.includes('REACT_APP_MICROSOFT_TENANT_ID')) {
    console.log('   ✅ Frontend: REACT_APP_MICROSOFT_TENANT_ID configured')
  } else {
    console.log('   ⚠️  Frontend: REACT_APP_MICROSOFT_TENANT_ID not configured')
  }
} else {
  console.log('   ❌ Frontend: .env file not found')
}

// Test 5: Check Route Configuration
console.log('\n5. Checking Route Configuration...')

// Check if user routes include Microsoft OAuth
const userRoutes = fs.readFileSync('packages/server/src/routes/user/index.ts', 'utf8')
if (userRoutes.includes('microsoftOAuthController')) {
  console.log('   ✅ Backend: Microsoft OAuth route configured')
} else {
  console.log('   ❌ Backend: Microsoft OAuth route not configured')
}

// Check if frontend routes include redirect page
const loginRoutes = fs.readFileSync('packages/ui/src/routes/LoginRoute.jsx', 'utf8')
if (loginRoutes.includes('MicrosoftRedirect')) {
  console.log('   ✅ Frontend: Microsoft redirect route configured')
} else {
  console.log('   ❌ Frontend: Microsoft redirect route not configured')
}

// Test 6: Check Database Schema
console.log('\n6. Checking Database Schema...')

const userEntity = fs.readFileSync('packages/server/src/database/entities/User.ts', 'utf8')
if (userEntity.includes('microsoftId')) {
  console.log('   ✅ Database: microsoftId field added to User entity')
} else {
  console.log('   ❌ Database: microsoftId field not added to User entity')
}

if (userEntity.includes('displayName')) {
  console.log('   ✅ Database: displayName field added to User entity')
} else {
  console.log('   ❌ Database: displayName field not added to User entity')
}

// Test 7: Check Documentation
console.log('\n7. Checking Documentation...')

const docFiles = [
  'MICROSOFT_OAUTH_IMPLEMENTATION.md',
  'MICROSOFT_OAUTH_SETUP.md',
  'MICROSOFT_OAUTH_TROUBLESHOOTING.md'
]

docFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`)
  } else {
    console.log(`   ⚠️  ${file} - Not found`)
  }
})

console.log('\n🔐 Microsoft OAuth Implementation Test Complete!\n')

console.log('Next Steps:')
console.log('1. Configure Azure Portal app registration')
console.log('2. Update environment variables with actual values')
console.log('3. Run database migration: pnpm typeorm:migration-run')
console.log('4. Install dependencies: pnpm install')
console.log('5. Start the application and test the login flow')
console.log('\nFor detailed setup instructions, see MICROSOFT_OAUTH_SETUP.md')
console.log('For troubleshooting, see MICROSOFT_OAUTH_TROUBLESHOOTING.md')
